-- Dumping structure for table essentialmode.weaponshop
DROP TABLE IF EXISTS `weaponshop`;
CREATE TABLE IF NOT EXISTS `weaponshop` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `zone` varchar(255) NOT NULL,
  `item` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `price` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Dumping data for table essentialmode.weaponshop: ~4 rows (approximately)
/*!40000 ALTER TABLE `weaponshop` DISABLE KEYS */;
INSERT INTO `weaponshop` (`id`, `zone`, `item`, `type`, `price`) VALUES
	(1, 'GunShop', 'WEAPON_PISTOL', 'weapon', 70000),
	(2, 'GunShop', 'WEAPON_FLASHLIGHT', 'weapon', 30000),
	(3, 'GunShop', 'silencer', 'item', 15000),
	(4, 'GunShop', 'clip', 'item', 3000);
/*!40000 ALTER TABLE `weaponshop` ENABLE KEYS */;