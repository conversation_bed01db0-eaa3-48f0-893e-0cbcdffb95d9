local QBCore = exports['qb-core']:GetCoreObject()

local shopItems = {}

CreateThread(function()
	local result = MySQL.query.await('SELECT * FROM weaponshop')
	if result then
		for i=1, #result, 1 do
			if shopItems[result[i].zone] == nil then
				shopItems[result[i].zone] = {}
			end
			local label
			if result[i].type == 'item' then
				label = QBCore.Shared.Items[result[i].item] and QBCore.Shared.Items[result[i].item].label or result[i].item
			else
			    label = QBCore.Shared.Weapons[result[i].item] and QBCore.Shared.Weapons[result[i].item].label or result[i].item
			end
			table.insert(shopItems[result[i].zone], {
				item  = result[i].item,
				price = result[i].price,
                type  = result[i].type,
				label = label
			})
		end
		TriggerClientEvent('weaponshop:sendShop', -1, shopItems)
	end
end)

QBCore.Functions.CreateCallback('weaponshop:getShop', function(source, cb)
	cb(shopItems)
end)


RegisterNetEvent('weaponshop:BuyWeapon', function(item, zone, isweapon, amount)
   local src = source
   local Player = QBCore.Functions.GetPlayer(src)
   local price = GetPrice(item, zone)

   	if price == 0 then
		print("Anti-cheat: Invalid price for item " .. item)
		return
	end


	if Player.PlayerData.money.cash >= price then

		if isweapon then
			Player.Functions.AddItem(item, 1, false, {ammo = 40})
			Player.Functions.RemoveMoney('cash', price)
			TriggerClientEvent('QBCore:Notify', src, 'You bought a weapon for $' .. price, 'success')
		else

			if not amount or amount == 0 then amount = 1 end
			if amount < 7 then
				local hasItem = Player.Functions.GetItemByName(item)
				local currentAmount = hasItem and hasItem.amount or 0

				if currentAmount + amount <= 6 then
					Player.Functions.AddItem(item, amount)
					Player.Functions.RemoveMoney('cash', price * amount)
					TriggerClientEvent('QBCore:Notify', src, 'You bought ' .. amount .. 'x ' .. item .. ' for $' .. (price * amount), 'success')
				else
					TriggerClientEvent('QBCore:Notify', src, 'Inventory shoma Faza Khali nadarad', 'error')
				end
			else
				TriggerClientEvent('QBCore:Notify', src, 'bishtar 6 ta nemitoni bekhari!', 'error')
			end

		end
	else
		TriggerClientEvent('QBCore:Notify', src, 'Pool shoma kam ast', 'error')
	end

end)

function GetPrice(weaponName, zone)
	local result = MySQL.query.await('SELECT price FROM weaponshop WHERE zone = ? AND item = ?', {zone, weaponName})

	if result and result[1] then
		return result[1].price
	else
		return 0
	end
end
