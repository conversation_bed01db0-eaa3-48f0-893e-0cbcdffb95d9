ESX = nil 
TriggerEvent("esx:getSharedObject", function(obj)
    ESX = obj
end)

local shopItems = {}

MySQL.ready(function()
	MySQL.Async.fetchAll('SELECT * FROM weaponshop', {}, function(result)
		for i=1, #result, 1 do
			if shopItems[result[i].zone] == nil then
				shopItems[result[i].zone] = {}
			end
			local label
			if result[i].type == 'item' then
				label =  ESX.GetItemLabel(result[i].item)
			else
			    label = ESX.GetWeaponLabel(result[i].item)
			end
			table.insert(shopItems[result[i].zone], {
				item  = result[i].item,
				price = result[i].price,
                type  = result[i].type,
				label = label
			})
		end
		TriggerClientEvent('weaponshop:sendShop', -1, shopItems)
	end)
end)

ESX.RegisterServerCallback('weaponshop:getShop', function(source, cb)
	cb(shopItems)
end)


RegisterServerEvent('weaponshop:BuyWeapon')
AddEventHandler("weaponshop:BuyWeapon", function(item, zone, isweapon, amount)
   local src = source
   local xPlayer = ESX.GetPlayerFromId(src) -- or ...
   local price = GetPrice(item, zone)
	
   	if price == 0 then
		print("Emerald_Ac_Ban")
		return
	end

    
	if  xPlayer.money >= price then -- or ((if xPlayer.getMoney() >= price then)) or ((if xPlayer.getAccount('black_money').money >= price then))

		if isweapon then

			xPlayer.addWeapon(item, 40)
			xPlayer.removeMoney(price) -- or ((xPlayer.removeMoney(price))) or ((xPlayer.removeAccountMoney('black_money', price)))

		else
		
			if not amount or amount == 0 then amount = 1 end
			if amount < 7 then 
				if xPlayer.getInventoryItem(item).count < amount then 
					xPlayer.addInventoryItem(item, amount) -- or ...
					xPlayer.removeMoney(price) -- or ((xPlayer.removeMoney(price))) or ((xPlayer.removeAccountMoney('black_money', price)))

				else
					TriggerClientEvent('esx:ShowNotification', xPlayer.source, 'Inventory shoma Faza Khali nadarad ')
				end
			else
				TriggerClientEvent('esx:ShowNotification', xPlayer.source, 'bishtar 6 ta nemitoni bekhari !')
			end
			
		end
	else
		TriggerClientEvent('esx:ShowNotification', xPlayer.source, _U('not_enough')) -- or xPlayer.ShowNotification(_U('not_enough')) 
	end
	
end)

function GetPrice(weaponName, zone)
	local price = MySQL.Sync.fetchScalar('SELECT price FROM weaponshop WHERE zone = @zone AND item = @item', {
		['@zone'] = zone,
		['@item'] = weaponName
	})

	if price then
		return price
	else
		return 0
	end
end
