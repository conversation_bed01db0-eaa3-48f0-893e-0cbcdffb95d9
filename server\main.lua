local QBCore = exports['qb-core']:GetCoreObject()

local shopItems = {}

-- Check if ps-inventory is available
CreateThread(function()
	Wait(1000)
	if GetResourceState('ps-inventory') == 'started' then
		print("ps-inventory detected and running")
	else
		print("WARNING: ps-inventory not found or not started!")
	end
end)

CreateThread(function()
	local result = MySQL.query.await('SELECT * FROM weaponshop')
	if result then
		print("Loading " .. #result .. " items from database") -- Debug
		for i=1, #result, 1 do
			if shopItems[result[i].zone] == nil then
				shopItems[result[i].zone] = {}
			end
			local label
			if result[i].type == 'item' then
				if QBCore.Shared.Items[result[i].item] then
					label = QBCore.Shared.Items[result[i].item].label
				else
					label = result[i].item
					print("WARNING: Item " .. result[i].item .. " not found in QBCore.Shared.Items")
				end
			else
				if QBCore.Shared.Weapons[result[i].item] then
					label = QBCore.Shared.Weapons[result[i].item].label
				else
					label = result[i].item
					print("WARNING: Weapon " .. result[i].item .. " not found in QBCore.Shared.Weapons")
				end
			end
			print("Adding item: " .. result[i].item .. " to zone: " .. result[i].zone .. " with label: " .. label) -- Debug
			table.insert(shopItems[result[i].zone], {
				item  = result[i].item,
				price = result[i].price,
                type  = result[i].type,
				label = label
			})
		end
		print("Shop items loaded. Sending to clients...") -- Debug
		TriggerClientEvent('weaponshop:sendShop', -1, shopItems)
	else
		print("No items found in weaponshop table") -- Debug
	end
end)

QBCore.Functions.CreateCallback('weaponshop:getShop', function(source, cb)
	cb(shopItems)
end)


RegisterNetEvent('weaponshop:BuyWeapon', function(item, zone, isweapon, amount)
   local src = source
   local Player = QBCore.Functions.GetPlayer(src)
   local price = GetPrice(item, zone)

   	if price == 0 then
		print("Anti-cheat: Invalid price for item " .. item)
		return
	end


	if Player.PlayerData.money.cash >= price then

		if isweapon then
			-- For weapons with ps-inventory
			local info = {
				ammo = 40,
				quality = 100
			}
			local success = false

			-- Try ps-inventory first
			if GetResourceState('ps-inventory') == 'started' then
				success = exports['ps-inventory']:AddItem(src, item, 1, false, info)
			else
				-- Fallback to QB-Core default
				success = Player.Functions.AddItem(item, 1, false, info)
			end

			if success then
				Player.Functions.RemoveMoney('cash', price)
				TriggerClientEvent('QBCore:Notify', src, 'Shoma yek selah kharidid baraye $' .. price, 'success')
				print("Player " .. src .. " bought weapon: " .. item .. " for $" .. price) -- Debug
			else
				TriggerClientEvent('QBCore:Notify', src, 'Inventory shoma por ast!', 'error')
			end
		else

			if not amount or amount == 0 then amount = 1 end
			if amount <= 10 then
				local totalPrice = price * amount
				if Player.PlayerData.money.cash >= totalPrice then
					local info = {
						quality = 100
					}
					local success = false

					-- Try ps-inventory first
					if GetResourceState('ps-inventory') == 'started' then
						success = exports['ps-inventory']:AddItem(src, item, amount, false, info)
					else
						-- Fallback to QB-Core default
						success = Player.Functions.AddItem(item, amount, false, info)
					end

					if success then
						Player.Functions.RemoveMoney('cash', totalPrice)
						TriggerClientEvent('QBCore:Notify', src, 'Shoma ' .. amount .. 'x ' .. item .. ' kharidid baraye $' .. totalPrice, 'success')
						print("Player " .. src .. " bought item: " .. item .. " x" .. amount .. " for $" .. totalPrice) -- Debug
					else
						TriggerClientEvent('QBCore:Notify', src, 'Inventory shoma por ast!', 'error')
					end
				else
					TriggerClientEvent('QBCore:Notify', src, 'Pool shoma kam ast', 'error')
				end
			else
				TriggerClientEvent('QBCore:Notify', src, 'bishtar 10 ta nemitoni bekhari!', 'error')
			end

		end
	else
		TriggerClientEvent('QBCore:Notify', src, 'Pool shoma kam ast', 'error')
	end

end)

function GetPrice(weaponName, zone)
	local result = MySQL.query.await('SELECT price FROM weaponshop WHERE zone = ? AND item = ?', {zone, weaponName})

	if result and result[1] then
		return result[1].price
	else
		return 0
	end
end
