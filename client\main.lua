local QBCore = exports['qb-core']:GetCoreObject()
local HasAlreadyEnteredMarker = false
local LastZone = nil
local CurrentAction = nil
local CurrentActionMsg = ''
local CurrentActionData = {}
local ShopOpen = false
local near = {active = false}

-- Custom DrawText3D function
local function DrawText3D(coords, text)
    if not coords or not text then return end
    local onScreen, _x, _y = World3dToScreen2d(coords.x, coords.y, coords.z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    local dist = GetDistanceBetweenCoords(px, py, pz, coords.x, coords.y, coords.z, 1)
    local scale = (1 / dist) * 2
    local fov = (1 / GetGameplayCamFov()) * 100
    local scale = scale * fov
    if onScreen then
        SetTextScale(0.0 * scale, 0.55 * scale)
        SetTextFont(0)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 215)
        SetTextDropshadow(0, 0, 0, 0, 255)
        SetTextEdge(2, 0, 0, 0, 150)
        SetTextDropShadow()
        SetTextOutline()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

-- Helper function for formatting numbers
local function CommaValue(amount)
    if not amount then return "0" end
    local formatted = amount
    while true do
        formatted, k = string.gsub(formatted, "^(-?%d+)(%d%d%d)", '%1,%2')
        if (k==0) then
            break
        end
    end
    return formatted
end

CreateThread(function()
    Wait(5000)
    QBCore.Functions.TriggerCallback('weaponshop:getShop', function(shopItems)
        if shopItems then
            for k,v in pairs(shopItems) do
                Config.Zones[k].Items = v
                print("Loaded " .. #v .. " items for zone: " .. k) -- Debug
            end
        else
            print("No shop items received from server") -- Debug
        end
    end)
end)

RegisterNetEvent('weaponshop:sendShop')
AddEventHandler('weaponshop:sendShop', function(shopItems)
    for k,v in pairs(shopItems) do
        Config.Zones[k].Items = v
    end
end)

function OpenShopMenu(zone)
    local elements = {}
    ShopOpen = true

    if Config.Blur then
        SetTimecycleModifier('hud_def_blur') -- blur
    end

    print("Opening shop for zone: " .. zone) -- Debug
    print("Items available: " .. (Config.Zones[zone].Items and #Config.Zones[zone].Items or 0)) -- Debug

    SendNUIMessage({
        display = true,
        clear = true
    })

    SetNuiFocus(true, true)

    if Config.Zones[zone].Items then
        for i=1, #Config.Zones[zone].Items, 1 do
            local item = Config.Zones[zone].Items[i]
            print("Sending item: " .. item.item .. " - " .. item.label .. " - $" .. item.price) -- Debug
            SendNUIMessage({
                itemLabel = item.label,
                item = item.item,
                price = CommaValue(item.price),
                desc = '',
                zone = zone,
                type = item.type
            })
        end
    else
        print("No items found for zone: " .. zone) -- Debug
    end
   -- PlaySoundFrontend(-1, 'BACK', 'HUD_AMMO_SHOP_SOUNDSET', false)
end


function DisplayBoughtScaleform(weaponName, price)
    local scaleform = RequestScaleformMovie('MP_BIG_MESSAGE_FREEMODE')
    local sec = 4

    BeginScaleformMovieMethod(scaleform, 'SHOW_WEAPON_PURCHASED')

    PushScaleformMovieMethodParameterString('Kharidi ba $' .. CommaValue(price))
    PushScaleformMovieMethodParameterString(QBCore.Shared.Weapons[weaponName] and QBCore.Shared.Weapons[weaponName].label or weaponName)
    PushScaleformMovieMethodParameterInt(GetHashKey(weaponName))
    PushScaleformMovieMethodParameterString('')
    PushScaleformMovieMethodParameterInt(100)

    EndScaleformMovieMethod()

   -- PlaySoundFrontend(-1, 'WEAPON_PURCHASE', 'HUD_AMMO_SHOP_SOUNDSET', false)

    CreateThread(function()
        while sec > 0 do
            Wait(0)
            sec = sec - 0.01

            DrawScaleformMovieFullscreen(scaleform, 255, 255, 255, 255)
        end
    end)
end

AddEventHandler('weaponshop:hasEnteredMarker', function(zone)
    if zone == 'GunShop' or zone == 'BlackWeashop' then
        CurrentAction     = 'shop_menu'
        CurrentActionMsg  = '[E] ra baraye Ammu-Nation kharid aslahe feshar dahid'
        CurrentActionData = { zone = zone }
    end
    if zone == 'Black' then
        CurrentAction     = 'shop_Black'
        CurrentActionMsg  = '[E] ra baraye Ammu-Nation kharid aslahe feshar dahid'
        CurrentActionData = { zone = zone }
    end
end)

AddEventHandler('weaponshop:hasExitedMarker', function(zone)
    CurrentAction = nil
    CurrentActionMsg = ''
    exports['qb-core']:HideText()
end)

AddEventHandler('onResourceStop', function(resource)
    if resource == GetCurrentResourceName() then
        if ShopOpen then
            SetNuiFocus(false, false)
            ShopOpen = false
        end
    end
end)

-- Create Blips
CreateThread(function()
    for k,v in pairs(Config.Zones) do
        if v.Legal then
            for i = 1, #v.Locations, 1 do
                local blip = AddBlipForCoord(v.Locations[i])

                SetBlipSprite (blip, 110)
                SetBlipDisplay(blip, 4)
                SetBlipScale  (blip, 1.0)
                SetBlipColour (blip, 1)
                SetBlipAsShortRange(blip, true)

                BeginTextCommandSetBlipName("STRING")
                AddTextComponentSubstringPlayerName('GunShop')
                EndTextCommandSetBlipName(blip)
            end
        end
    end
end)

-- Display markers
CreateThread(function()
	while true do
        Wait(3)
		if near.active then
			DrawMarker(6, near.coords, 0.0, 0.0, 0.0, 0, 0.0, 0.0, Config.Size.x, Config.Size.y, Config.Size.z, Config.Color.r, Config.Color.g, Config.Color.b, 100, false, true, 2, false, false, false, false)
            DrawMarker(29, near.coords, 0.0, 0.0, 0.0, 0, 0.0, 0.0, Config.Size.x, Config.Size.y, Config.Size.z, Config.Color.r, Config.Color.g, Config.Color.b, 100, false, true, 2, false, false, false, false)
		else
			Wait(1000)
		end
	end
end)

function NearAny()
    local coords = GetEntityCoords(PlayerPedId())

    for k,v in pairs(Config.Zones) do
        for i=1, #v.Locations, 1 do
            if Vdist(v.Locations[i].x, v.Locations[i].y, v.Locations[i].z, coords) < Config.DrawDistance then
                near = {active = true, coords = vector3(v.Locations[i].x, v.Locations[i].y, v.Locations[i].z) }
                return
            end
        end
    end

    near = {active = false}
end

CreateThread(function()
    while true do
        Wait(1000)
        NearAny()
    end
end)


-- Enter / Exit marker events
CreateThread(function()
	while true do
		Wait(1000)
		local coords = GetEntityCoords(PlayerPedId())
		local isInMarker, currentZone = false, nil

		for k,v in pairs(Config.Zones) do
			for i=1, #v.Locations, 1 do
				if GetDistanceBetweenCoords(coords, v.Locations[i], true) < Config.Size.x then
					isInMarker, ShopItems, currentZone, LastZone = true, v.Items, k, k
				end
			end
		end
		if isInMarker and not HasAlreadyEnteredMarker then
			HasAlreadyEnteredMarker = true
			TriggerEvent('weaponshop:hasEnteredMarker', currentZone)
		end

		if not isInMarker and HasAlreadyEnteredMarker then
			HasAlreadyEnteredMarker = false
			TriggerEvent('weaponshop:hasExitedMarker', LastZone)
		end
	end
end)

CreateThread(function()
    while true do
        Wait(0)
        if CurrentAction and CurrentActionMsg then
            -- Use QB-Core's built-in text display
            exports['qb-core']:DrawText(CurrentActionMsg, 'left')
            if IsControlJustReleased(0, 38) then -- E key
                exports['qb-core']:HideText()
                if CurrentAction == 'shop_menu' then
                    if Config.LicenseEnable and Config.Zones[CurrentActionData.zone].Legal then
                        -- You can implement weapon license check here if needed
                        -- For now, we'll allow access to legal shops
                        OpenShopMenu(CurrentActionData.zone)
                    else
                        OpenShopMenu(CurrentActionData.zone)
                    end
                    CurrentAction = nil
                elseif CurrentAction == 'shop_Black' then
                    OpenShopMenu(CurrentActionData.zone)
                    CurrentAction = nil
                end
            end
        else
            exports['qb-core']:HideText()
            Wait(500)
        end

        -- Handle ESC key to close shop
        if ShopOpen and IsControlJustReleased(0, 322) then -- ESC key
            SetNuiFocus(false, false)
            ShopOpen = false
            if Config.Blur then
                SetTimecycleModifier('default')
            end
            SendNUIMessage({
                display = false
            })
        end
    end
end)

RegisterNUICallback('buyItem', function(data, cb)
        local weaponhash = GetHashKey(data.item)
        local isweapon = false
        if IsWeaponValid(weaponhash) then
            isweapon = true
            TriggerServerEvent('weaponshop:BuyWeapon', data.item, data.zone, isweapon)

        end
        if not isweapon then
            local dialog = exports['qb-input']:ShowInput({
                header = "Weapon Shop",
                submitText = "Buy",
                inputs = {
                    {
                        text = "Amount",
                        name = "amount",
                        type = "number",
                        isRequired = true,
                        default = 1
                    }
                }
            })

            if dialog then
                local amount = tonumber(dialog.amount)
                if amount and amount > 0 then
                    TriggerServerEvent('weaponshop:BuyWeapon', data.item, data.zone, isweapon, amount)
                else
                    QBCore.Functions.Notify('Shoma Chizi Vared Nakardid', 'error')
                end
            end
        end
        cb('ok')
end)

RegisterNUICallback('focusOff', function(data, cb)
    SetNuiFocus(false, false)
    FreezeEntityPosition(PlayerPedId(), false)
    ShopOpen = false
    if Config.Blur then
        SetTimecycleModifier('default') -- remove blur
    end
    cb('ok')
end)


