ESX                           = nil
local HasAlreadyEnteredMarker = false
local LastZone = nil
local CurrentAction = nil
local CurrentActionMsg = ''
local CurrentActionData = {}
local ShopOpen = false
local near = {active = false}

Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(0)
    end
end)

Citizen.CreateThread(function()
    Wait(5000)
    ESX.TriggerServerCallback('weaponshop:getShop', function(shopItems)
        for k,v in pairs(shopItems) do
            Config.Zones[k].Items = v
        end
    end)
end)

RegisterNetEvent('weaponshop:sendShop')
AddEventHandler('weaponshop:sendShop', function(shopItems)
    for k,v in pairs(shopItems) do
        Config.Zones[k].Items = v
    end
end)

function OpenShopMenu(zone)
    local elements = {}
    ShopOpen = true

    if Config.Blur then
    SetTimecycleModifier('hud_def_blur') -- blur
    end

    SendNUIMessage({
        display = true,
        clear = true
    })

    SetNuiFocus(true, true)

    for i=1, #Config.Zones[zone].Items, 1 do
        local item = Config.Zones[zone].Items[i]
        SendNUIMessage({
            itemLabel = item.label,
            item = item.item,
            price = ESX.Math.GroupDigits(item.price),
            desc = '',
            zone = zone,
            type = item.type
        })
    end

    ESX.UI.Menu.CloseAll()
   -- PlaySoundFrontend(-1, 'BACK', 'HUD_AMMO_SHOP_SOUNDSET', false)
end


function DisplayBoughtScaleform(weaponName, price)
    local scaleform = ESX.Scaleform.Utils.RequestScaleformMovie('MP_BIG_MESSAGE_FREEMODE')
    local sec = 4

    BeginScaleformMovieMethod(scaleform, 'SHOW_WEAPON_PURCHASED')

    PushScaleformMovieMethodParameterString(_U('weapon_bought', ESX.Math.GroupDigits(price)))
    PushScaleformMovieMethodParameterString(ESX.GetWeaponLabel(weaponName))
    PushScaleformMovieMethodParameterInt(GetHashKey(weaponName))
    PushScaleformMovieMethodParameterString('')
    PushScaleformMovieMethodParameterInt(100)

    EndScaleformMovieMethod()

   -- PlaySoundFrontend(-1, 'WEAPON_PURCHASE', 'HUD_AMMO_SHOP_SOUNDSET', false)

    Citizen.CreateThread(function()
        while sec > 0 do
            Citizen.Wait(0)
            sec = sec - 0.01
    
            DrawScaleformMovieFullscreen(scaleform, 255, 255, 255, 255)
        end
    end)
end

AddEventHandler('weaponshop:hasEnteredMarker', function(zone)
    if zone == 'GunShop' or zone == 'BlackWeashop' then
        CurrentAction     = 'shop_menu'
        CurrentActionMsg  = _U('shop_menu_prompt')
        CurrentActionData = { zone = zone }
    end
    if zone == 'Black' then
        CurrentAction     = 'shop_Black'
        CurrentActionMsg  = _U('shop_menu_prompt')
        CurrentActionData = { zone = zone }
    end
end)

AddEventHandler(':hasExitedMarker', function(zone)
    CurrentAction = nil
    ESX.UI.Menu.CloseAll()
end)

AddEventHandler('onResourceStop', function(resource)
    if resource == GetCurrentResourceName() then
        if ShopOpen then
            ESX.UI.Menu.CloseAll()
        end
    end
end)

-- Create Blips
Citizen.CreateThread(function()
    for k,v in pairs(Config.Zones) do
        if v.Legal then
            for i = 1, #v.Locations, 1 do
                local blip = AddBlipForCoord(v.Locations[i])

                SetBlipSprite (blip, 110)
                SetBlipDisplay(blip, 4)
                SetBlipScale  (blip, 1.0)
                SetBlipColour (blip, 1)
                SetBlipAsShortRange(blip, true)

                BeginTextCommandSetBlipName("STRING")
                AddTextComponentSubstringPlayerName(_U('map_blip'))
                EndTextCommandSetBlipName(blip)
            end
        end
    end
end)

-- Display markers
Citizen.CreateThread(function()
	while true do
        Citizen.Wait(3)
		if near.active then
			DrawMarker(6, near.coords, 0.0, 0.0, 0.0, 0, 0.0, 0.0, Config.Size.x, Config.Size.y, Config.Size.z, Config.Color.r, Config.Color.g, Config.Color.b, 100, false, true, 2, false, false, false, false)
            DrawMarker(29, near.coords, 0.0, 0.0, 0.0, 0, 0.0, 0.0, Config.Size.x, Config.Size.y, Config.Size.z, Config.Color.r, Config.Color.g, Config.Color.b, 100, false, true, 2, false, false, false, false)
		else
			Citizen.Wait(1000)
		end
	end
end)

function NearAny()
    local coords = GetEntityCoords(PlayerPedId())

    for k,v in pairs(Config.Zones) do
        for i=1, #v.Locations, 1 do
            if Vdist(v.Locations[i].x, v.Locations[i].y, v.Locations[i].z, coords) < Config.DrawDistance then
                near = {active = true, coords = vector3(v.Locations[i].x, v.Locations[i].y, v.Locations[i].z) }
                return
            end
        end
    end

    near = {active = false}
end

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)
        NearAny()
    end
end)


-- Enter / Exit marker events
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(1000)
		local coords = GetEntityCoords(PlayerPedId())
		local isInMarker, currentZone = false, nil

		for k,v in pairs(Config.Zones) do
			for i=1, #v.Locations, 1 do
				if GetDistanceBetweenCoords(coords, v.Locations[i], true) < Config.Size.x then
					isInMarker, ShopItems, currentZone, LastZone = true, v.Items, k, k
				end
			end
		end
		if isInMarker and not HasAlreadyEnteredMarker then
			HasAlreadyEnteredMarker = true
			TriggerEvent('weaponshop:hasEnteredMarker', currentZone)
		end
		
		if not isInMarker and HasAlreadyEnteredMarker then
			HasAlreadyEnteredMarker = false
			TriggerEvent('weaponshop:hasExitedMarker', LastZone)
		end
	end
end)

AddEventHandler("onKeyDown", function(key)
    if key == 'e' then
        if CurrentAction == 'shop_menu' then
            if Config.LicenseEnable and Config.Zones[CurrentActionData.zone].Legal then
                ESX.TriggerServerCallback('esx_license:checkLicense', function(hasWeaponLicense)
                    hasWeaponLicense = true
                    if hasWeaponLicense then
                        OpenShopMenu(CurrentActionData.zone)
                    else
                        ESX.ShowNotification('~r~Shoma License Aslahe Ra Nadarid Baraye Daryaft Be~b~ LSPD ~r~Morajee Konid')
                    end
                end, GetPlayerServerId(PlayerId()), 'weapon')
            else
                OpenShopMenu(CurrentActionData.zone)
            end
            CurrentAction = nil
        elseif CurrentAction == 'shop_Black' then
            OpenShopMenu(CurrentActionData.zone)
        end
    end
end)

RegisterNUICallback('buyItem', function(data, cb)
        local weaponhash = GetHashKey(data.item)
        local isweapon = false
        if IsWeaponValid(weaponhash) then
            isweapon = true
            TriggerServerEvent('weaponshop:BuyWeapon', data.item, data.zone, isweapon)
            
        end
        if not isweapon then
            ESX.UI.Menu.Open("dialog", GetCurrentResourceName(), "amount", {
				title = 'Tedad Mored Niaz'
			}, function(data2, menu2)
				local amount = tonumber(data2.value)
				if (amount and amount > 0) then
                    
                    TriggerServerEvent('weaponshop:BuyWeapon', data.item, data.zone, isweapon, amount)
                    menu2.close()
 
				else
					ESX.ShowNotification('~r~Shoma Chizi Vared Nakardid')
					
				end
			end, function(data2, menu2)
				menu2.close()
			end)

        end
        
end)

RegisterNUICallback('focusOff', function(data, cb)
    SetNuiFocus(false, false)
    FreezeEntityPosition(PlayerPedId(), false)
    if Config.Blur then 
        SetTimecycleModifier('default') -- remove blur
    end
end)       


