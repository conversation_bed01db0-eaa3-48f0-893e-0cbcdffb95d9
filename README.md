# QB-WeaponShop

A weapon shop script converted from ESX to QB-Core framework with ox_mysql support.

## Features

- Multiple weapon shop locations
- Legal and illegal weapon shops
- Interactive NUI interface
- Weapon license system (optional)
- Support for both weapons and items
- Configurable prices and locations
- Blip system for legal shops
- Persian/Farsi language support

## Dependencies

- [qb-core](https://github.com/qbcore-framework/qb-core)
- [oxmysql](https://github.com/overextended/oxmysql)
- [qb-input](https://github.com/qbcore-framework/qb-input) (for item quantity input)

## Installation

1. Download and extract the resource to your `resources` folder and rename it to `qb-weaponshop`
2. Import the SQL file `qb_weaponshop.sql` into your database
3. Add `ensure qb-weaponshop` to your `server.cfg`
4. Configure the weapon shop locations and items in `config.lua`
5. Make sure you have the required dependencies installed
6. Restart your server

## Troubleshooting

If you see "No items found" in the shop:
1. Make sure you imported the SQL file correctly
2. Check server console for debug messages
3. Verify that the weapon/item names in the database match your QB-Core shared items/weapons

If the mouse stays on screen after closing:
- Press ESC key to close the shop properly
- The script now handles this automatically

## Configuration

### Config.lua

- `Config.LicenseEnable`: Set to `true` if you want to use weapon license system
- `Config.Zones`: Configure different weapon shop zones and their locations
- `Config.DrawDistance`: Distance for marker visibility
- `Config.Size`: Marker size configuration
- `Config.Color`: Marker color (RGB)

### Database Configuration

The script uses the `weaponshop` table with the following structure:
- `zone`: The zone name (GunShop, Black, Cazino, etc.)
- `item`: The weapon/item name (must match QB-Core shared items/weapons)
- `type`: Either 'weapon' or 'item'
- `price`: The price in cash

## Usage

1. Go to any configured weapon shop location
2. Press `E` to open the shop menu
3. Browse available weapons and items
4. Click on items to purchase them
5. For items, you'll be prompted to enter the quantity

## Weapon Shop Zones

- **GunShop**: Legal weapon shops (visible on map with blips)
- **Black**: Illegal weapon shops (hidden, no blips)
- **Cazino**: Special casino weapon shop

## Customization

You can easily add new weapon shop locations by editing the `Config.Zones` table in `config.lua`. Each zone can have multiple locations and different items.

## Support

This script has been converted from ESX to QB-Core. If you encounter any issues, please check:
1. All dependencies are installed and running
2. Database table is properly imported
3. QB-Core shared items/weapons match the database entries

## Credits

- Original ESX version converted to QB-Core
- Uses ox_mysql for database operations
- Compatible with QB-Core framework
