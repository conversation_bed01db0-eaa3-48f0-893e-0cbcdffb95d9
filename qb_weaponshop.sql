-- QB-Core Weapon Shop SQL
-- Compatible with ox_mysql

DROP TABLE IF EXISTS `weaponshop`;
CREATE TABLE IF NOT EXISTS `weaponshop` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `zone` varchar(255) NOT NULL,
  `item` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `price` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Sample data for QB-Core weapon shop
INSERT INTO `weaponshop` (`id`, `zone`, `item`, `type`, `price`) VALUES
	(1, 'GunShop', 'weapon_pistol', 'weapon', 70000),
	(2, 'GunShop', 'weapon_flashlight', 'weapon', 30000),
	(3, 'GunShop', 'pistol_suppressor', 'item', 15000),
	(4, 'GunShop', 'pistol_extendedclip', 'item', 3000),
	(5, 'GunShop', 'weapon_knife', 'weapon', 5000),
	(6, 'GunShop', 'weapon_bat', 'weapon', 8000),
	(7, 'Black', 'weapon_microsmg', 'weapon', 150000),
	(8, 'Black', 'weapon_assaultrifle', 'weapon', 250000),
	(9, 'Black', 'rifle_suppressor', 'item', 25000),
	(10, 'Black', 'rifle_extendedclip', 'item', 8000);
